import cv2
import matplotlib.pyplot as plt

# Load the image
image = cv2.imread('1.jpg')

# Convert the image to grayscale
gray_image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

# Apply adaptive thresholding
adaptive_thresh = cv2.adaptiveThreshold(
    gray_image,       # Grayscale image
    255,              # Maximum value
    cv2.ADAPTIVE_THRESH_MEAN_C,  # Adaptive method (mean of neighborhood)
    cv2.THRESH_BINARY,  # Thresholding type (binary)
    11,                # Block size (size of neighborhood area)
    2                  # Constant subtracted from the mean
)

# Show the original image and thresholded result
plt.figure(figsize=(10, 5))

# Original image
plt.subplot(1, 2, 1)
plt.imshow(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
plt.title('Original Image')
plt.axis('off')

# Adaptive thresholded image
plt.subplot(1, 2, 2)
plt.imshow(adaptive_thresh, cmap='gray')
plt.title('Adaptive Thresholding')
plt.axis('off')

plt.show()
